export interface ItemRecord {
  createBy?: number; // 创建者
  createDept?: number; // 创建部门
  createTime?: Date; // 创建时间
  id: number; // 主键
  isAsc?: string; // 排序的方向desc或者asc
  isCompleted?: number; // 是否完成（1=是，0=否）
  itemId: string; // 关联检修项ID
  itemName?: string; // 检修项名称
  itemNum: string; // 检修项编号
  orderByColumn?: string; // 排序列
  pageNum?: number; // 当前页数
  pageSize?: number; // 分页大小
  params?: { [key: string]: { [key: string]: any } }; // 请求参数
  pointAbnormal?: number; // 本项下异常点数
  pointCompleted?: number; // 本项下已完成点数
  pointTotal?: number; // 本项下总检修点数
  remark?: string; // 备注
  taskId: string; // 关联任务ID
  updateBy?: number; // 更新者
  updateTime?: Date; // 更新时间
  typeValues?: string | string[];
}

export interface PageQuery {
  pageNum?: number;
  pageSize?: number;
  orderByColumn?: string;
  isAsc?: string;
}
