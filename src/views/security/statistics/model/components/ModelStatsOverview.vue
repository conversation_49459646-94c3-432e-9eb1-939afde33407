<template>
  <div class="model-stats-overview bg-white p-4 mx-4 mt-4 rounded-lg">
    <!-- 统计卡片 -->
    <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
      <div
        v-for="(item, index) of statsModel"
        :key="index"
        class="stat-card bg-[rgba(0,52,119,0.04)] rounded-lg p-4 flex items-center space-x-3 cursor-pointer"
      >
        <img :src="item.icon" alt="" class="object-contain size-12" />

        <div class="flex-1 w-0 overflow-hidden flex items-center">
          <span class="text-sm text-gray-600">{{ item.label }}</span>
          <div class="text-2xl font-bold text-primary-700 flex-1 w-0 text-right">
            {{ item.value || '0' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, watch } from 'vue';
  import { selectModelStats } from '@/api/security/modelStats';
  import type { BiCountModelVo } from '@/api/security/modelStats/model';
  import stats1 from '@/assets/images/statistics/model/stats-1.png?url';
  import stats2 from '@/assets/images/statistics/model/stats-2.png?url';
  import stats3 from '@/assets/images/statistics/model/stats-3.png?url';
  import stats4 from '@/assets/images/statistics/task/stats-4.png?url';

  defineOptions({ name: 'ModelStatsOverview' });

  interface Props {
    searchParams?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    searchParams: () => ({}),
  });

  const statsData = ref<BiCountModelVo>({
    apiCallSuccessRate: '0%',
    averageResponseTime: '0ms',
    todayCallCount: '0',
    totalCallCount: '0',
  });

  const statsModel = computed(() => {
    return [
      {
        label: '接口调用成功率',
        value: statsData.value.apiCallSuccessRate,
        icon: stats1,
      },
      {
        label: '平均响应时间',
        value: `${statsData.value.averageResponseTime}ms`,
        icon: stats2,
      },
      {
        label: '今日调用次数',
        value: statsData.value.todayCallCount,
        icon: stats3,
      },
      {
        label: '总调用次数',
        value: statsData.value.totalCallCount,
        icon: stats4,
      },
    ];
  });

  // 获取统计数据
  async function fetchStatsData(params?: any) {
    try {
      const response = await selectModelStats();
      statsData.value = response || {
        apiCallSuccessRate: '0%',
        averageResponseTime: '0ms',
        todayCallCount: '0',
        totalCallCount: '0',
      };
    } catch (error) {
      console.error('获取模型统计数据失败:', error);
      statsData.value = {
        apiCallSuccessRate: '0%',
        averageResponseTime: '0ms',
        todayCallCount: '0',
        totalCallCount: '0',
      };
    }
  }

  // 监听查询参数变化
  watch(
    () => props.searchParams,
    (newParams) => {
      fetchStatsData(newParams);
    },
    { deep: true, immediate: false },
  );

  onMounted(() => {
    fetchStatsData(props.searchParams);
  });

  // 暴露刷新方法供父组件调用
  defineExpose({
    refresh: (params?: any) => fetchStatsData(params),
  });
</script>

<style scoped>
  .stat-card {
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  }
</style>
