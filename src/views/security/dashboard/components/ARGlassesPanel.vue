<template>
  <div class="ar-glasses-panel">
    <div class="panel-header">
      <div class="header-left">
        <h3 class="panel-title">AR眼镜实时画面</h3>
        <div class="online-status">
          <span class="status-dot online"></span>
          <span class="status-text">在线设备: {{ onlineCount }}/{{ totalCount }}</span>
        </div>
      </div>

      <div class="header-right">
        <Button type="primary" size="small" @click="refreshStreams">
          <Icon icon="ant-design:reload-outlined" />
          刷新
        </Button>
      </div>
    </div>

    <div class="streams-container">
      <Scrollable direction="horizontal" :speed="1" :key="scrollableKey">
        <div
          v-for="stream in streamsData"
          :key="stream.id"
          class="stream-card"
          :class="{ active: selectedStreamId === stream.id }"
        >
          <div class="stream-header">
            <span class="stream-title">{{ stream.name }}</span>
            <div class="stream-status" :class="stream.status">
              <span class="status-dot" :class="stream.status"></span>
              <span class="status-text">{{ stream.status === 'online' ? '在线' : '离线' }}</span>
            </div>
          </div>

          <div class="stream-content">
            <div class="video-placeholder h-full">
              <LivePlayer
                v-if="stream.streamUrl"
                :url="stream.streamUrl"
                :headers="stream.streamHeaders"
                v-model:status="stream.status"
                class="live-player"
              />
              <div v-else class="placeholder-content">
                <Icon icon="ant-design:disconnect-outlined" :size="48" />
                <span>设备离线</span>
              </div>
            </div>
          </div>

          <div class="stream-footer">
            <div class="stream-info">
              <span v-if="stream.scene" class="info-item">场景: {{ stream.scene }}</span>
            </div>
          </div>
        </div>
      </Scrollable>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, watch, watchEffect } from 'vue';
  import { Button, message } from 'ant-design-vue';
  import Icon from '@/components/Icon/Icon.vue';
  import LivePlayer from '@/components/LivePlayer/index.vue';
  import Scrollable from '@/plugins/scrollable/components/Scrollable/index.vue';
  import { getArGlassRealTimeScreenStats } from '@/api/bi/dashboard';
  import type { BiArGlassRealTimeScreenStatsVo } from '@/api/bi/model';

  // 定义 props
  interface Props {
    siteName?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    siteName: undefined,
  });

  const selectedStreamId = ref<number | null>(null);

  // 数据状态
  const arGlassApiData = ref<BiArGlassRealTimeScreenStatsVo[]>([]);
  const streamsLoading = ref(false);

  // 转换API数据为组件需要的格式
  interface StreamData {
    id: number;
    name: string;
    status: 'online' | 'offline';
    scene: string;
    lastUpdate: string;
    streamUrl: string | null;
    streamHeaders: object;
  }

  const streamsData = ref<StreamData[]>([]);

  watchEffect(() => {
    streamsData.value = arGlassApiData.value.map((item, index) => ({
      id: index + 1,
      name: item.deviceNo || `AR-${String(index + 1).padStart(3, '0')}`,
      status: item.webrtcAddress ? 'online' : 'offline',
      scene: item.model,
      lastUpdate: '刚刚更新',
      streamUrl: item.webrtcAddress || null,
      streamHeaders: {
        authorization: item.webrtcAuthorization,
      },
    }));
  });

  // 获取AR眼镜实时画面统计数据
  async function fetchArGlassData() {
    try {
      streamsLoading.value = true;
      const data = await getArGlassRealTimeScreenStats({
        siteName: props.siteName,
      });
      arGlassApiData.value = data;
    } catch (error) {
      console.error('获取AR眼镜实时画面统计数据失败:', error);
      // message.error('获取AR眼镜实时画面统计数据失败');
      arGlassApiData.value = [];
    } finally {
      streamsLoading.value = false;
    }
  }

  // 计算属性
  const onlineCount = computed(
    () => streamsData.value.filter((stream) => stream.status === 'online').length,
  );

  const totalCount = computed(() => streamsData.value.length);

  // 方法
  // function selectStream(stream: StreamData) {
  //   selectedStreamId.value = stream.id;
  //   message.info(`选择了设备: ${stream.name}`);
  // }

  const scrollableKey = ref(0);

  async function refreshStreams() {
    await fetchArGlassData();
    message.success('刷新成功');
    ++scrollableKey.value;
  }

  // 监听站点变化，重新获取数据
  watch(
    () => props.siteName,
    async () => {
      await fetchArGlassData();
      // 重置选中的流
      selectedStreamId.value = null;
    },
  );

  // 初始化
  onMounted(async () => {
    await fetchArGlassData();
  });
</script>

<style scoped>
  /* 响应式设计 */
  @media (max-width: 1200px) {
    .panel-header {
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      gap: 20px;
    }

    .header-right {
      align-self: auto;
    }

    .stream-card {
      width: 260px;
    }
  }

  @media (max-width: 768px) {
    .stream-card {
      width: 200px;
    }

    .stream-content {
      height: 120px;
    }
  }

  .ar-glasses-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }

  .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .panel-title {
    margin: 0;
    color: #fff;
    font-size: 18px;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 50%);
  }

  .online-status {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }

  .status-dot.online {
    background-color: #27ae60;
    box-shadow: 0 0 10px rgba(39, 174, 96, 60%);
  }

  .status-text {
    color: rgba(255, 255, 255, 90%);
    font-size: 14px;
  }

  .header-right {
    display: flex;
    gap: 12px;
  }

  .streams-container {
    flex: 1;
    overflow: hidden;
  }

  .stream-card {
    flex-shrink: 0;
    width: 280px;
    margin-top: 8px;
    margin-right: 20px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 20%);
    border-radius: 8px;
    background: rgba(255, 255, 255, 10%);
    cursor: pointer;
  }

  .stream-card:hover,
  .stream-card.active {
    transform: translateY(-5px);
    border-color: rgba(255, 255, 255, 40%);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 40%);
  }

  .stream-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 10%);
  }

  .stream-title {
    color: #fff;
    font-size: 14px;
    font-weight: 600;
  }

  .stream-status {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .stream-status .status-dot.online {
    background-color: #27ae60;
  }

  .stream-status .status-dot.offline {
    background-color: #95a5a6;
  }

  .stream-status .status-text {
    font-size: 12px;
  }

  .stream-content {
    position: relative;
    height: 160px;
    background: rgba(0, 0, 0, 30%);
  }

  .live-player {
    width: 100%;
    height: 100%;

    /* 防止 iframe 在拖拽时被选中 */
    user-select: none;
  }

  .placeholder-content,
  .offline-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: rgba(255, 255, 255, 60%);
    gap: 8px;
  }

  .stream-footer {
    padding: 12px;
  }

  .stream-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .info-item {
    color: rgba(255, 255, 255, 70%);
    font-size: 12px;
  }
</style>
