<template>
  <div class="dashboard-header">
    <!-- 左侧标题 -->
    <div class="header-left">
      <h1 class="header-title">
        <span class="title-zh">数据概览</span>
        <span class="title-en">Data Overview</span>
      </h1>
    </div>

    <!-- 右侧功能区 -->
    <div class="header-right">
      <!-- 站点选择器 -->
      <div class="site-selector">
        <a-select
          v-model:value="selectedSiteName"
          placeholder="请选择站点"
          style="width: 200px"
          allowClear
          :options="stationOptions"
          :field-names="{ label: 'stationName', value: 'stationName' }"
          :loading="stationLoading"
          @change="handleSiteChange"
        />
      </div>

      <!-- 当前时间 -->
      <div class="current-time">
        <span class="time-text">{{ currentTime }}</span>
      </div>

      <!-- 全屏按钮 -->
      <div class="fullscreen-btn" @click="handleFullscreen">
        <Icon
          :icon="
            isFullscreen ? 'ant-design:fullscreen-exit-outlined' : 'ant-design:fullscreen-outlined'
          "
          :size="24"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue';
  import { useRoute } from 'vue-router';
  import { Select as ASelect } from 'ant-design-vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { stationOptionSelect } from '@/api/business/station';

  const emit = defineEmits(['toggle-fullscreen', 'site-change']);
  const route = useRoute();

  const currentTime = ref('');
  const isFullscreen = ref(false);
  let timeInterval: NodeJS.Timeout | null = null;

  // 站点选择器相关
  const selectedSiteName = ref<string | undefined>();
  const stationOptions = ref<any[]>([]);
  const stationLoading = ref(false);

  // 获取站点选项
  async function fetchStationOptions() {
    try {
      stationLoading.value = true;
      const response = await stationOptionSelect();
      stationOptions.value = response || [];
    } catch (error) {
      console.error('获取站点选项失败:', error);
      // message.error('获取站点选项失败');
      stationOptions.value = [];
    } finally {
      stationLoading.value = false;
    }
  }

  // 处理站点变化
  function handleSiteChange(siteName: any, _option: any) {
    selectedSiteName.value = siteName;
    emit('site-change', siteName);
  }

  // 更新时间
  function updateTime() {
    const now = new Date();
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const weekday = weekdays[now.getDay()];
    const time = now.toLocaleTimeString('zh-CN', { hour12: false });
    const date = now.toLocaleDateString('zh-CN').replace(/\//g, '/');

    currentTime.value = `${weekday} ${time} ${date}`;
  }

  // 处理全屏切换
  function handleFullscreen() {
    emit('toggle-fullscreen');
  }

  // 监听全屏状态变化
  function handleFullscreenChange() {
    // 检查是否是视频元素全屏（iframe 内的 video 元素）
    const fullscreenElement = document.fullscreenElement;
    const isVideoFullscreen =
      fullscreenElement &&
      (fullscreenElement.tagName === 'VIDEO' ||
        fullscreenElement.tagName === 'IFRAME' ||
        fullscreenElement.querySelector('video'));

    // 如果是视频全屏，不触发页面级别的全屏逻辑
    if (isVideoFullscreen) {
      return;
    }

    // 综合考虑浏览器全屏状态和路由参数
    const browserFullscreen = !!document.fullscreenElement;
    const routeFullscreen = !!route.query.__full__;
    isFullscreen.value = browserFullscreen || routeFullscreen;
  }

  // 监听路由变化
  function handleRouteChange() {
    handleFullscreenChange();
  }

  onMounted(async () => {
    updateTime();
    timeInterval = setInterval(updateTime, 1000);

    // 初始化全屏状态
    handleFullscreenChange();

    // 监听浏览器全屏状态变化
    document.addEventListener('fullscreenchange', handleFullscreenChange);

    // 监听路由变化（用于检测 __full__ 参数变化）
    window.addEventListener('popstate', handleRouteChange);

    // 获取站点选项
    await fetchStationOptions();
  });

  onUnmounted(() => {
    if (timeInterval) {
      clearInterval(timeInterval);
    }
    document.removeEventListener('fullscreenchange', handleFullscreenChange);
    window.removeEventListener('popstate', handleRouteChange);
  });
</script>

<style scoped>
  /* 响应式设计 */
  @media (max-width: 1200px) {
    .dashboard-header {
      height: 75px;
    }

    .title-zh {
      font-size: 26px;
    }

    .title-en {
      font-size: 13px;
    }

    .time-text {
      font-size: 15px;
    }
  }

  @media (max-width: 768px) {
    .dashboard-header {
      height: 60px;
      padding: 0 15px;
    }

    .title-zh {
      font-size: 20px;
    }

    .title-en {
      display: none;
    }

    .time-text {
      font-size: 12px;
    }
  }

  .dashboard-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;

    /* padding: 0 40px; */

    /* border: 1px solid rgba(255, 255, 255, 10%); */

    /* border-radius: 12px; */

    /* background: linear-gradient(135deg, rgba(11, 36, 59, 90%) 0%, rgba(46, 134, 193, 80%) 100%); */

    /* box-shadow: 0 8px 32px rgba(0, 0, 0, 30%); */

    /* backdrop-filter: blur(10px); */
  }

  .header-left {
    display: flex;
    align-items: center;
  }

  .header-title {
    display: flex;
    flex-direction: column;
    margin: 0;
    line-height: 1.2;
  }

  .title-zh {
    color: #fff;
    font-size: 28px;
    font-weight: 700;
    letter-spacing: 2px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 50%);
  }

  .title-en {
    margin-top: 2px;
    color: rgba(255, 255, 255, 80%);
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 1px;
    text-transform: uppercase;
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .site-selector {
    display: flex;
    align-items: center;
  }

  .site-selector :deep(.ant-select) {
    border: 1px solid rgba(255, 255, 255, 20%);
    border-radius: 8px;
    background: rgba(255, 255, 255, 10%);
  }

  .site-selector :deep(.ant-select-selector) {
    border: none !important;
    background: transparent !important;
    box-shadow: none !important;
    color: #fff !important;
  }

  .site-selector :deep(.ant-select-selection-placeholder) {
    color: rgba(255, 255, 255, 60%) !important;
  }

  .site-selector :deep(.ant-select-arrow) {
    color: rgba(255, 255, 255, 80%) !important;
  }

  .site-selector :deep(.ant-select:hover .ant-select-selector) {
    border-color: rgba(255, 255, 255, 40%) !important;
  }

  .site-selector :deep(.ant-select-focused .ant-select-selector) {
    border-color: rgba(255, 255, 255, 60%) !important;
  }

  .current-time {
    display: flex;
    align-items: center;
    padding: 7px 20px;
    border: 1px solid rgba(255, 255, 255, 20%);
    border-radius: 8px;
    background: rgba(255, 255, 255, 10%);
  }

  .time-text {
    color: #fff;
    font-family: Consolas, Monaco, monospace;
    font-size: 16px;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 30%);
  }

  .fullscreen-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 20%);
    border-radius: 8px;
    background: rgba(255, 255, 255, 10%);
    color: #fff;
    cursor: pointer;
  }

  .fullscreen-btn:hover {
    transform: translateY(-2px);
    border-color: rgba(255, 255, 255, 40%);
    background: rgba(255, 255, 255, 20%);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 30%);
  }

  .fullscreen-btn:active {
    transform: translateY(0);
  }
</style>
