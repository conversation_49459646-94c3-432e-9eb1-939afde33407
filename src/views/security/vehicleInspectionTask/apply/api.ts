import { defHttp } from '@/utils/http/axios';

// 人员信息接口类型定义（复用仓库申请页面的类型）
export interface WorkerInfo {
  id: string | number;
  workerNum?: string;
  userName?: string;
  gender?: string;
  type?: string;
  image?: string;
  status?: string;
  imageUrl?: string;
  qualificationInformation?: string | null;
  stationId?: string;
  stationName?: string | null;
  phone?: string;
  violationCount?: number | null;
  remark?: string | null;
  broadcastStatus?: number | null;
  promptStatus?: number | null;
}

// 车辆检修工单表单数据类型定义
export interface VehicleTaskForm {
  userId?: string;
  userName?: string;
  workerNum?: string;
  workArea?: string;
  workVehicle?: string;
  workStDriver?: string;
  workStDriverId?: string;
  workCoDriver?: string;
  workCoDriverId?: string;
  workTicketNo?: string;
}

// API 响应类型定义
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  token: null;
  uri: null;
  data: T;
  request_time: null;
  response_time: null;
  cost_time: null;
  debug_image_url: null;
}

/**
 * 新增车辆检修工单
 */
export function addVehicleTask(data: VehicleTaskForm) {
  return defHttp.post<ApiResponse>({
    url: '/business/VehicleTask/add',
    data,
  });
}
