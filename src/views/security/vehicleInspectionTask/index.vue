<template>
  <PageWrapper dense>
    <!-- 统计概览 -->
    <TaskStatsOverview ref="statsOverviewRef" :search-params="searchParams" />

    <BasicTable @register="registerTable">
      <template #toolbar>
        <Space>
          <a-button
            class="<sm:hidden"
            @click="
              downloadExcel(inspectionTaskExport, '车辆检修任务管理', getForm().getFieldsValue())
            "
            >导出</a-button
          >
          <a-button
            class="<sm:hidden"
            type="primary"
            danger
            @click="multipleRemove(inspectionTaskRemove)"
            :disabled="!selected"
            >删除</a-button
          >
          <!-- <a-button type="primary" @click="handleAdd">新增</a-button> -->
        </Space>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            stopButtonPropagation
            :actions="[
              // {
              //   label: '实时画面',
              //   icon: IconEnum.PLAY,
              //   type: 'primary',
              //   ghost: true,
              //   onClick: handleLiveStream.bind(null, record),
              // },
              // {
              //   label: '查看录播',
              //   icon: IconEnum.NEXT,
              //   type: 'primary',
              //   ghost: true,
              //   onClick: handleRecord.bind(null, record),
              // },
              {
                label: '查看',
                icon: IconEnum.PREVIEW,
                type: 'primary',
                ghost: true,
                onClick: handleInfo.bind(null, record),
              },
              // {
              //   label: '修改',
              //   icon: IconEnum.EDIT,
              //   type: 'primary',
              //   ghost: true,
              //   onClick: handleEdit.bind(null, record),
              // },
              {
                label: '检修报告',
                icon: IconEnum.DETAIL,
                type: 'primary',
                ghost: true,
                onClick: handleInspectionReport.bind(null, record),
              },
              {
                label: '删除',
                icon: IconEnum.DELETE,
                type: 'primary',
                danger: true,
                ghost: true,
                popConfirm: {
                  placement: 'left',
                  title: `是否确认删除?`,
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <InspectionTaskModal @register="registerModal" @reload="reload" />
    <InspectionTaskInfoDrawer
      v-bind="{
        handleLiveStream,
        handleRecord,
      }"
      @register="registerInfoDrawer"
    />
    <ControlTaskRecordModal width="80%" @register="registerRecordModal" />
    <LiveStreamModal @register="registerLiveStreamModal" />
    <InspectionReportModal width="90%" @register="registerInspectionReportModal" />
  </PageWrapper>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { PageWrapper } from '@/components/Page';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { Space } from 'ant-design-vue';
  import {
    inspectionTaskList,
    inspectionTaskExport,
    inspectionTaskRemove,
  } from '@/api/security/vehicleInspectionTask';
  import InspectionTaskModal from './Modal.vue';
  import { useModal } from '@/components/Modal';
  import { useDrawer } from '@/components/Drawer';
  import { downloadExcel } from '@/utils/file/download';
  import { formSchemas, columns } from './data';
  import { IconEnum } from '@/enums/appEnum';
  import InspectionTaskInfoDrawer from './InspectionTaskInfoDrawer.vue';
  import ControlTaskRecordModal from './RecordModal.vue';
  import { LiveStreamModal } from '@/components/LiveStreamModal';
  import InspectionReportModal from './InspectionReportModal.vue';
  import TaskStatsOverview from './TaskStatsOverview.vue';

  defineOptions({ name: 'InspectionTask' });

  const statsOverviewRef = ref();
  const searchParams = ref({});

  const [registerTable, { reload: tableReload, multipleRemove, selected, getForm }] = useTable({
    showTableSetting: false,
    rowSelection: {
      type: 'checkbox',
    },
    title: '车辆检修任务',
    showIndexColumn: false,
    api: inspectionTaskList,
    rowKey: 'id',
    useSearchForm: true,
    beforeFetch: (params) => {
      // 更新查询参数，触发统计组件更新
      searchParams.value = { ...params };
      return params;
    },
    formConfig: {
      schemas: formSchemas,
      labelWidth: 100,
      name: 'inspectionTask',
      baseColProps: {
        xs: 24,
        sm: 24,
        md: 24,
        lg: 6,
      },
      // 日期选择格式化
      fieldMapToTime: [
        [
          'startTime',
          ['params[startTimeBegin]', 'params[startTimeEnd]'],
          ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59'],
        ],
        [
          'endTime',
          ['params[endTimeBegin]', 'params[endTimeEnd]'],
          ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59'],
        ],
      ],
    },
    columns: columns,
    actionColumn: {
      width: 300,
      title: '操作',
      key: 'action',
      fixed: 'right',
    },
  });

  const [registerModal, { openModal }] = useModal();

  // 自定义 reload 函数，同时刷新表格和统计数据
  async function reload() {
    await tableReload();
    if (statsOverviewRef.value?.refresh) {
      await statsOverviewRef.value.refresh(searchParams.value);
    }
  }

  // function handleEdit(record: Recordable) {
  //   openModal(true, { record, update: true });
  // }

  // function handleAdd() {
  //   openModal(true, { update: false });
  // }

  async function handleDelete(record: Recordable) {
    const { id } = record;
    await inspectionTaskRemove([id]);
    await reload();
  }

  const [registerInfoDrawer, { openDrawer: openInfoDrawer }] = useDrawer();

  const currentRecord = ref();
  function handleInfo(record: Recordable) {
    const { id } = record;
    currentRecord.value = record;
    openInfoDrawer(true, id);
  }

  const [registerRecordModal, { openModal: openRecordModal }] = useModal();

  function handleRecord(record: Recordable) {
    const { id: taskId } = record;
    openRecordModal(true, {
      taskId,
      taskType: '1',
    });
  }

  const [registerLiveStreamModal, { openModal: openLiveStreamModal }] = useModal();

  function handleLiveStream(record: Recordable) {
    const { webrtcAddress, webRtcAuthorization, workTicketNoOri } = {
      ...currentRecord.value,
      ...record,
    };

    const url = webrtcAddress;
    const token = webRtcAuthorization;

    openLiveStreamModal(true, {
      url,
      token,
      title: `实时监控画面 - ${workTicketNoOri}`,
      width: '80%',
      height: '600px',
    });
  }

  const [registerInspectionReportModal, { openModal: openInspectionReportModal }] = useModal();

  function handleInspectionReport(record: Recordable) {
    const { id } = record;
    openInspectionReportModal(true, id);
  }
</script>

<style scoped></style>
