import { BasicColumn } from '@/components/Table';
import { FormSchema } from '@/components/Form';
import { useRender } from '@/hooks/component/useRender';
import { getDictOptions, getDict } from '@/utils/dict';
import { DictEnum } from '@/enums/dictEnum';
import { Image } from 'ant-design-vue';

export const { renderDict, renderDictTags } = useRender();

export const columns: BasicColumn[] = [
  {
    title: '检修项示意图',
    dataIndex: 'itemImg',
    customRender({ value }) {
      if (value) {
        return <Image src={value} alt="检修项示意图" height={50} class="!object-contain" />;
      }
      return '无';
    },
  },
  { title: '检修项编号', dataIndex: 'itemNum' },
  { title: '检修项名称', dataIndex: 'itemName' },
  {
    title: '是否完成',
    dataIndex: 'isCompleted',
    customRender({ value }) {
      return renderDict(value, DictEnum.VEHICLE_ITEM_COMPLETED);
    },
  },
  { title: '标准流程说明', dataIndex: 'standardProcess', width: 300, align: 'left' },
  {
    title: '目标检测类别',
    dataIndex: 'typeNames',
  },
  {
    title: '已检测类别',
    dataIndex: 'sendNames',
  },
  { title: '顺序', dataIndex: 'orderNum' },
  { title: '贴纸数字', dataIndex: 'number' },
  { title: '适用车型', dataIndex: 'vehicleType' },
  { title: '部件类型', dataIndex: 'componentType' },
  { title: '检修时长', dataIndex: 'costTimeStr' },
  { title: '检修开始', dataIndex: 'startTime' },
  { title: '检修结束', dataIndex: 'endTime' },
  { title: '备注', dataIndex: 'remark' },
  { title: '创建时间', dataIndex: 'createTime' },
];

export const formSchemas: FormSchema[] = [
  {
    label: '检修项编号',
    field: 'itemNum',
    component: 'Input',
  },
  {
    label: '检修项名称',
    field: 'itemName',
    component: 'Input',
  },
  {
    label: '是否完成',
    field: 'isCompleted',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.VEHICLE_ITEM_COMPLETED),
    },
  },
];

export const modalSchemas: FormSchema[] = [
  {
    field: 'id',
    label: '主键',
    component: 'Input',
    show: false,
  },
  {
    label: '关联任务ID',
    field: 'taskId',
    component: 'Input',
    show: false,
  },
  {
    label: '关联检修项ID',
    field: 'itemId',
    component: 'Input',
    show: false,
  },
  // {
  //   label: '检修项编号',
  //   field: 'itemNum',
  //   component: 'Input',
  //   required: true,
  // },
  {
    label: '检修项名称',
    field: 'itemName',
    component: 'Input',
    required: true,
  },
  {
    label: '顺序',
    field: 'orderNum',
    component: 'InputNumber',
    required: true,
  },
  {
    label: '贴纸数字',
    field: 'number',
    component: 'InputNumber',
    required: true,
  },
  {
    label: '目标检测类别',
    field: 'typeValues',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.VEHICLE_ITEM_TYPES),
      mode: 'multiple',
    },
  },
  {
    label: '标准流程说明',
    field: 'standardProcess',
    component: 'InputTextArea',
    required: true,
  },
  {
    label: '适用车型',
    field: 'vehicleType',
    component: 'Input',
  },
  {
    label: '部件类型',
    field: 'componentType',
    component: 'Input',
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
  },
];
