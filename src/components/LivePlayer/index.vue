<template>
  <iframe
    :key="props.path"
    ref="iframeRef"
    :src="iframeSrc"
    frameborder="0"
    allowfullscreen
    loading="lazy"
    :style="{
      width: '100%',
      height: '100%',
    }"
  ></iframe>
</template>

<script setup>
  import { ref, watchEffect, onMounted, onUnmounted, toRaw } from 'vue';
  import { DEFAULT_STATUS } from './constants.js';

  const props = defineProps({
    path: {
      type: String,
      default: '',
    },
    url: {
      type: String,
      default: '',
    },
    headers: {
      type: Object,
      default: () => ({
        authorization: 'Basic YWRtaW46YWRtaW4xMjM=',
      }),
    },
    manual: {
      type: Boolean,
      default: false,
    },
    status: {
      type: String,
      default: DEFAULT_STATUS,
    },
  });

  const emit = defineEmits(['update:status']);

  const iframeRef = ref(null);

  const baseUrl = window.location.origin + import.meta.env.VITE_PUBLIC_PATH;

  const iframeSrc = `${baseUrl}/media/index.html`;

  // 监听来自 iframe 的消息
  const handleMessage = (event) => {
    // 确保消息来自我们的 iframe
    if (event.source !== iframeRef.value?.contentWindow) {
      return;
    }

    if (event.data.type === 'statusUpdate') {
      const status = event.data.status;
      // 更新父组件的状态
      emit('update:status', status);
    }
  };

  onMounted(() => {
    window.addEventListener('message', handleMessage);
  });

  onUnmounted(() => {
    window.removeEventListener('message', handleMessage);
  });

  watchEffect(() => {
    if (!iframeRef.value) {
      return false;
    }

    iframeRef.value.onload = () => {
      const params = {
        type: 'injectParams',
        url: props.url,
        path: props.path,
        headers: toRaw(props.headers),
        manual: props.manual,
      };

      iframeRef.value.contentWindow.postMessage(params, '*');
    };
  });
</script>
