/**
 * LivePlayer 组件状态常量
 */

// 播放器状态枚举
export const PLAYER_STATUS = {
  ONLINE: 'online', // 在线 - 拉流播放成功
  OFFLINE: 'offline', // 离线 - 连接失败或设备离线
  CONNECTING: 'connecting', // 连接中 - 正在尝试连接
  WAITING: 'waiting', // 等待连接 - 手动模式下等待用户操作
};

// 状态显示文本映射
export const STATUS_TEXT_MAP = {
  [PLAYER_STATUS.ONLINE]: '在线',
  [PLAYER_STATUS.OFFLINE]: '离线',
  [PLAYER_STATUS.CONNECTING]: '连接中',
  [PLAYER_STATUS.WAITING]: '等待连接',
};

// 状态样式类映射
export const STATUS_CLASS_MAP = {
  [PLAYER_STATUS.ONLINE]: 'online',
  [PLAYER_STATUS.OFFLINE]: 'offline',
  [PLAYER_STATUS.CONNECTING]: 'connecting',
  [PLAYER_STATUS.WAITING]: 'connecting', // 等待连接使用连接中的样式
};

// 获取状态显示文本
export const getStatusText = (status) => {
  return STATUS_TEXT_MAP[status] || status;
};

// 获取状态样式类
export const getStatusClass = (status) => {
  return STATUS_CLASS_MAP[status] || 'offline';
};

// 判断是否为在线状态
export const isOnline = (status) => {
  return status === PLAYER_STATUS.ONLINE;
};

// 判断是否为离线状态
export const isOffline = (status) => {
  return status === PLAYER_STATUS.OFFLINE;
};

// 判断是否为连接中状态
export const isConnecting = (status) => {
  return status === PLAYER_STATUS.CONNECTING || status === PLAYER_STATUS.WAITING;
};

// 所有状态列表
export const ALL_STATUS = Object.values(PLAYER_STATUS);

// 默认状态
export const DEFAULT_STATUS = PLAYER_STATUS.OFFLINE;
