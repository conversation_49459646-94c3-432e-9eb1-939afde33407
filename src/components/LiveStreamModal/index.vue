<template>
  <BasicModal
    v-bind="$attrs"
    :title="title"
    :width="width"
    @register="registerModal"
    @ok="handleOk"
    @cancel="handleCancel"
    :showOkBtn="false"
    :showCancelBtn="false"
  >
    <!-- 只有在弹窗打开时才渲染直播组件 -->
    <div v-if="getOpen" class="h-[564px] overflow-hidden">
      <!-- 单个视频流 -->
      <LivePlayer
        v-if="!isMultiStream"
        :path="streamPath"
        :url="streamUrl"
        :headers="streamHeaders"
        class="w-full h-full"
      />

      <!-- 多个视频流 - 使用 Tabs 切换 -->
      <div v-else class="h-full flex flex-col">
        <Tabs
          v-model:activeKey="activeTabKey"
          size="small"
          class="flex-none a-tabs--single"
          @change="handleTabChange"
        >
          <TabPane v-for="stream in streams" :key="stream.key" :tab="stream.title" />
        </Tabs>

        <!-- 当前选中的视频流播放器 -->
        <div class="flex-1 h-0 overflow-hidden">
          <LivePlayer
            v-if="currentStream"
            :key="currentStream.key"
            :path="currentStream.path"
            :url="currentStream.url"
            :headers="currentStream.headers"
            class="h-full"
          />
          <div v-else class="w-full h-full flex items-center justify-center text-gray-400">
            <span>请选择视频流</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 弹窗未打开时显示占位内容 -->
    <div v-else class="h-[564px] overflow-hidden flex items-center justify-center text-gray-400">
      <span>等待加载...</span>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import LivePlayer from '@/components/LivePlayer/index.vue';
  import { Tabs, TabPane } from 'ant-design-vue';

  interface StreamConfig {
    key: string;
    title: string;
    path: string;
    url: string;
    headers: object;
  }

  const title = ref('实时监控画面');
  const width = ref('80%');
  const streamPath = ref('');
  const streams = ref<StreamConfig[]>([]);
  const activeTabKey = ref<string>('');

  const isMultiStream = computed(() => streams.value.length > 0);

  // 当前选中的视频流
  const currentStream = computed(() => {
    if (!activeTabKey.value || !streams.value.length) {
      return null;
    }
    return streams.value.find((stream) => stream.key === activeTabKey.value) || null;
  });

  const streamUrl = ref('');
  const streamHeaders = ref({});

  const [registerModal, { closeModal, getOpen }] = useModalInner((data) => {
    if (data) {
      if (data.url) {
        streamUrl.value = data.url;
        streams.value = [];
        activeTabKey.value = '';
        title.value = data.title || '实时监控画面';
        streamHeaders.value = {
          authorization: data.token,
        };
      } else if (data.path) {
        // 单个视频流
        streamPath.value = data.path;
        streams.value = [];
        activeTabKey.value = '';
        title.value = data.title || '实时监控画面';
      } else if (data.streams) {
        // 多个视频流
        streams.value = data.streams;
        streamPath.value = '';
        // 默认选中第一个视频流
        activeTabKey.value = data.streams.length > 0 ? data.streams[0].key : '';
        title.value = data.title || '实时监控画面';
      }
    }
  });

  // 处理 Tab 切换
  function handleTabChange(key: string | number) {
    activeTabKey.value = String(key);
  }

  function handleOk() {
    closeModal();
  }

  function handleCancel() {
    closeModal();
  }
</script>

<style scoped></style>
